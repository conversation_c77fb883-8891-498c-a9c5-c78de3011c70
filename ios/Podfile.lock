PODS:
  - boost (1.84.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - fast_float (8.0.0)
  - FBLazyVector (0.80.1)
  - fmt (11.0.2)
  - glog (0.3.5)
  - hermes-engine (0.80.1):
    - hermes-engine/Pre-built (= 0.80.1)
  - hermes-engine/Pre-built (0.80.1)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - RCT-Folly (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Default (= 2024.11.18.00)
  - RCT-Folly/Default (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
  - RCT-Folly/Fabric (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
  - RCTDeprecation (0.80.1)
  - RCTRequired (0.80.1)
  - RCTTypeSafety (0.80.1):
    - FBLazyVector (= 0.80.1)
    - RCTRequired (= 0.80.1)
    - React-Core (= 0.80.1)
  - React (0.80.1):
    - React-Core (= 0.80.1)
    - React-Core/DevSupport (= 0.80.1)
    - React-Core/RCTWebSocket (= 0.80.1)
    - React-RCTActionSheet (= 0.80.1)
    - React-RCTAnimation (= 0.80.1)
    - React-RCTBlob (= 0.80.1)
    - React-RCTImage (= 0.80.1)
    - React-RCTLinking (= 0.80.1)
    - React-RCTNetwork (= 0.80.1)
    - React-RCTSettings (= 0.80.1)
    - React-RCTText (= 0.80.1)
    - React-RCTVibration (= 0.80.1)
  - React-callinvoker (0.80.1)
  - React-Core (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.80.1)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/CoreModulesHeaders (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/Default (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/DevSupport (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.80.1)
    - React-Core/RCTWebSocket (= 0.80.1)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTAnimationHeaders (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTBlobHeaders (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTImageHeaders (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTLinkingHeaders (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTNetworkHeaders (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTSettingsHeaders (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTTextHeaders (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTVibrationHeaders (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTWebSocket (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.80.1)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-CoreModules (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety (= 0.80.1)
    - React-Core/CoreModulesHeaders (= 0.80.1)
    - React-jsi (= 0.80.1)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTFBReactNativeSpec
    - React-RCTImage (= 0.80.1)
    - ReactCommon
    - SocketRocket
  - React-cxxreact (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.80.1)
    - React-debug (= 0.80.1)
    - React-jsi (= 0.80.1)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-logger (= 0.80.1)
    - React-perflogger (= 0.80.1)
    - React-runtimeexecutor (= 0.80.1)
    - React-timing (= 0.80.1)
    - SocketRocket
  - React-debug (0.80.1)
  - React-defaultsnativemodule (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-domnativemodule
    - React-featureflagsnativemodule
    - React-hermes
    - React-idlecallbacksnativemodule
    - React-jsi
    - React-jsiexecutor
    - React-microtasksnativemodule
    - React-RCTFBReactNativeSpec
    - SocketRocket
  - React-domnativemodule (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Fabric
    - React-FabricComponents
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-Fabric (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.80.1)
    - React-Fabric/attributedstring (= 0.80.1)
    - React-Fabric/componentregistry (= 0.80.1)
    - React-Fabric/componentregistrynative (= 0.80.1)
    - React-Fabric/components (= 0.80.1)
    - React-Fabric/consistency (= 0.80.1)
    - React-Fabric/core (= 0.80.1)
    - React-Fabric/dom (= 0.80.1)
    - React-Fabric/imagemanager (= 0.80.1)
    - React-Fabric/leakchecker (= 0.80.1)
    - React-Fabric/mounting (= 0.80.1)
    - React-Fabric/observers (= 0.80.1)
    - React-Fabric/scheduler (= 0.80.1)
    - React-Fabric/telemetry (= 0.80.1)
    - React-Fabric/templateprocessor (= 0.80.1)
    - React-Fabric/uimanager (= 0.80.1)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/animations (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/attributedstring (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/componentregistry (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/componentregistrynative (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.80.1)
    - React-Fabric/components/root (= 0.80.1)
    - React-Fabric/components/scrollview (= 0.80.1)
    - React-Fabric/components/view (= 0.80.1)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/legacyviewmanagerinterop (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/root (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/scrollview (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/view (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-renderercss
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-Fabric/consistency (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/core (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/dom (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/imagemanager (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/leakchecker (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/mounting (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/observers (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.80.1)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/observers/events (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/scheduler (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/telemetry (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/templateprocessor (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/uimanager (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.80.1)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/uimanager/consistency (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-FabricComponents (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.80.1)
    - React-FabricComponents/textlayoutmanager (= 0.80.1)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.80.1)
    - React-FabricComponents/components/iostextinput (= 0.80.1)
    - React-FabricComponents/components/modal (= 0.80.1)
    - React-FabricComponents/components/rncore (= 0.80.1)
    - React-FabricComponents/components/safeareaview (= 0.80.1)
    - React-FabricComponents/components/scrollview (= 0.80.1)
    - React-FabricComponents/components/text (= 0.80.1)
    - React-FabricComponents/components/textinput (= 0.80.1)
    - React-FabricComponents/components/unimplementedview (= 0.80.1)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/iostextinput (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/modal (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/rncore (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/safeareaview (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/scrollview (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/text (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/textinput (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricImage (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired (= 0.80.1)
    - RCTTypeSafety (= 0.80.1)
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.80.1)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - SocketRocket
    - Yoga
  - React-featureflags (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-featureflagsnativemodule (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-graphics (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-utils
    - SocketRocket
  - React-hermes (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.80.1)
    - React-jsi
    - React-jsiexecutor (= 0.80.1)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.80.1)
    - React-runtimeexecutor
    - SocketRocket
  - React-idlecallbacksnativemodule (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-ImageManager (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
    - SocketRocket
  - React-jserrorhandler (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - ReactCommon/turbomodule/bridging
    - SocketRocket
  - React-jsi (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-jsiexecutor (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.80.1)
    - React-jsi (= 0.80.1)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.80.1)
    - SocketRocket
  - React-jsinspector (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-perflogger (= 0.80.1)
    - React-runtimeexecutor (= 0.80.1)
    - SocketRocket
  - React-jsinspectorcdp (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-jsinspectornetwork (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-jsinspectorcdp
    - SocketRocket
  - React-jsinspectortracing (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-oscompat
    - SocketRocket
  - React-jsitooling (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.80.1)
    - React-jsi (= 0.80.1)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - SocketRocket
  - React-jsitracing (0.80.1):
    - React-jsi
  - React-logger (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-Mapbuffer (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - SocketRocket
  - React-microtasksnativemodule (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - SocketRocket
  - react-native-safe-area-context (5.5.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common (= 5.5.2)
    - react-native-safe-area-context/fabric (= 5.5.2)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-safe-area-context/common (5.5.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-safe-area-context/fabric (5.5.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-NativeModulesApple (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-oscompat (0.80.1)
  - React-perflogger (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-performancetimeline (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsinspectortracing
    - React-perflogger
    - React-timing
    - SocketRocket
  - React-RCTActionSheet (0.80.1):
    - React-Core/RCTActionSheetHeaders (= 0.80.1)
  - React-RCTAnimation (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-featureflags
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTAppDelegate (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsitooling
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTNetwork
    - React-RCTRuntime
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimescheduler
    - React-utils
    - ReactCommon
    - SocketRocket
  - React-RCTBlob (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - SocketRocket
  - React-RCTFabric (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-performancetimeline
    - React-RCTAnimation
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-renderercss
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-RCTFBReactNativeSpec (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - ReactCommon
    - SocketRocket
  - React-RCTImage (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - SocketRocket
  - React-RCTLinking (0.80.1):
    - React-Core/RCTLinkingHeaders (= 0.80.1)
    - React-jsi (= 0.80.1)
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.80.1)
  - React-RCTNetwork (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTRuntime (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - SocketRocket
  - React-RCTSettings (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTText (0.80.1):
    - React-Core/RCTTextHeaders (= 0.80.1)
    - Yoga
  - React-RCTVibration (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-rendererconsistency (0.80.1)
  - React-renderercss (0.80.1):
    - React-debug
    - React-utils
  - React-rendererdebug (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - SocketRocket
  - React-rncore (0.80.1)
  - React-RuntimeApple (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - SocketRocket
  - React-RuntimeCore (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact
    - React-Fabric
    - React-featureflags
    - React-hermes
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
  - React-runtimeexecutor (0.80.1):
    - React-jsi (= 0.80.1)
  - React-RuntimeHermes (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-jsitracing
    - React-RuntimeCore
    - React-utils
    - SocketRocket
  - React-runtimescheduler (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspectortracing
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
    - SocketRocket
  - React-timing (0.80.1)
  - React-utils (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - React-hermes
    - React-jsi (= 0.80.1)
    - SocketRocket
  - ReactAppDependencyProvider (0.80.1):
    - ReactCodegen
  - ReactCodegen (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
  - ReactCommon (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - ReactCommon/turbomodule (= 0.80.1)
    - SocketRocket
  - ReactCommon/turbomodule (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.80.1)
    - React-cxxreact (= 0.80.1)
    - React-jsi (= 0.80.1)
    - React-logger (= 0.80.1)
    - React-perflogger (= 0.80.1)
    - ReactCommon/turbomodule/bridging (= 0.80.1)
    - ReactCommon/turbomodule/core (= 0.80.1)
    - SocketRocket
  - ReactCommon/turbomodule/bridging (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.80.1)
    - React-cxxreact (= 0.80.1)
    - React-jsi (= 0.80.1)
    - React-logger (= 0.80.1)
    - React-perflogger (= 0.80.1)
    - SocketRocket
  - ReactCommon/turbomodule/core (0.80.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.80.1)
    - React-cxxreact (= 0.80.1)
    - React-debug (= 0.80.1)
    - React-featureflags (= 0.80.1)
    - React-jsi (= 0.80.1)
    - React-logger (= 0.80.1)
    - React-perflogger (= 0.80.1)
    - React-utils (= 0.80.1)
    - SocketRocket
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFlashList (1.8.3):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNGestureHandler (2.27.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNImageCropPicker (0.50.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNImageCropPicker/QBImagePickerController (= 0.50.1)
    - SocketRocket
    - TOCropViewController (~> 2.7.4)
    - Yoga
  - RNImageCropPicker/QBImagePickerController (0.50.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - TOCropViewController (~> 2.7.4)
    - Yoga
  - RNReanimated (3.18.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.18.0)
    - RNReanimated/worklets (= 3.18.0)
    - SocketRocket
    - Yoga
  - RNReanimated/reanimated (3.18.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.18.0)
    - SocketRocket
    - Yoga
  - RNReanimated/reanimated/apple (3.18.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNReanimated/worklets (3.18.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/worklets/apple (= 3.18.0)
    - SocketRocket
    - Yoga
  - RNReanimated/worklets/apple (3.18.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNScreens (4.12.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNScreens/common (= 4.12.0)
    - SocketRocket
    - Yoga
  - RNScreens/common (4.12.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNSVG (15.12.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNSVG/common (= 15.12.0)
    - SocketRocket
    - Yoga
  - RNSVG/common (15.12.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNVectorIcons (10.2.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - SocketRocket (0.7.1)
  - TOCropViewController (2.7.4)
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - fast_float (from `../node_modules/react-native/third-party-podspecs/fast_float.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsinspectorcdp (from `../node_modules/react-native/ReactCommon/jsinspector-modern/cdp`)
  - React-jsinspectornetwork (from `../node_modules/react-native/ReactCommon/jsinspector-modern/network`)
  - React-jsinspectortracing (from `../node_modules/react-native/ReactCommon/jsinspector-modern/tracing`)
  - React-jsitooling (from `../node_modules/react-native/ReactCommon/jsitooling`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-oscompat (from `../node_modules/react-native/ReactCommon/oscompat`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTFBReactNativeSpec (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTRuntime (from `../node_modules/react-native/React/Runtime`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-renderercss (from `../node_modules/react-native/ReactCommon/react/renderer/css`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactAppDependencyProvider (from `build/generated/ios`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFlashList (from `../node_modules/@shopify/flash-list`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - SocketRocket (~> 0.7.1)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - libwebp
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket
    - TOCropViewController

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  fast_float:
    :podspec: "../node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2025-05-06-RNv0.80.0-4eb6132a5bf0450bf4c6c91987675381d7ac8bca
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsinspectorcdp:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/cdp"
  React-jsinspectornetwork:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/network"
  React-jsinspectortracing:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/tracing"
  React-jsitooling:
    :path: "../node_modules/react-native/ReactCommon/jsitooling"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-oscompat:
    :path: "../node_modules/react-native/ReactCommon/oscompat"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTFBReactNativeSpec:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTRuntime:
    :path: "../node_modules/react-native/React/Runtime"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-renderercss:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/css"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactAppDependencyProvider:
    :path: build/generated/ios
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFlashList:
    :path: "../node_modules/@shopify/flash-list"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 7e761d76ca2ce687f7cc98e698152abd03a18f90
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  DoubleConversion: cb417026b2400c8f53ae97020b2be961b59470cb
  fast_float: b32c788ed9c6a8c584d114d0047beda9664e7cc6
  FBLazyVector: 09f03e4b6f42f955734b64a118f86509cc719427
  fmt: a40bb5bd0294ea969aaaba240a927bd33d878cdd
  glog: 5683914934d5b6e4240e497e0f4a3b42d1854183
  hermes-engine: 4f07404533b808de66cf48ac4200463068d0e95a
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  RCT-Folly: 59ec0ac1f2f39672a0c6e6cecdd39383b764646f
  RCTDeprecation: efa5010912100e944a7ac9a93a157e1def1988fe
  RCTRequired: bbc4cf999ddc4a4b076e076c74dd1d39d0254630
  RCTTypeSafety: d877728097547d0a37786cc9130c43ad71739ac3
  React: 4b0b9cb962e694611e5e8a697c1b0300a2510c21
  React-callinvoker: 70f125c17c7132811a6b473946ac5e7ae93b5e57
  React-Core: 7cbc3118df2334b2ef597d9a515938b02c82109f
  React-CoreModules: 7d8c14ecb889e7786a04637583b55b7d8f246baf
  React-cxxreact: f32be07cba236c2f20f4e05ca200577ba5358e78
  React-debug: deb3a146ef717fa3e8f4c23e0288369fe53199b7
  React-defaultsnativemodule: 2c13a4240c5f96c42d069d1ba2392de6b4145bbd
  React-domnativemodule: 91349b0b1cb20310cec1341b87cdd461aaa85e57
  React-Fabric: bdfc7ec2481f26d7a9b8f59461f29ba4d903c549
  React-FabricComponents: 47898469543d1bfb4528a9846419ec5568be89b1
  React-FabricImage: ac8fc85ef452e5e9ae935c41118814651bd9e7f3
  React-featureflags: 793b911e4c53e680db4a7d9965d0d6dc87b2fa88
  React-featureflagsnativemodule: 25c9516d0dd004493c9bbafeb97da20bf9bde7dc
  React-graphics: e07281690425dd9eeba3875d1faad28bc1f6da3b
  React-hermes: bc1440d0e0662cc813bbf1c5ffbf9e0db2993a0f
  React-idlecallbacksnativemodule: a2a3bb4a1793280b34d06d00169153b094be8c16
  React-ImageManager: c9fa7461f3cab08e7bc98cbf55455b499e71c8b3
  React-jserrorhandler: 15e591702040afed99cfcd088cf2337a8d09d807
  React-jsi: 512ab3a1a628bc8824c41de8bcbbb81b2ac6fa8d
  React-jsiexecutor: 653ccd2dee1e5ea558eecaf2f27b8bba0f09add8
  React-jsinspector: 9121ccd2676a3f7c079ac01c9f90183422e3190e
  React-jsinspectorcdp: 5c723ff2a09d73f2fdc496a545fb7003e7fdc079
  React-jsinspectornetwork: 9cb0173f69e8405cef33fc79030fad26bbc3c073
  React-jsinspectortracing: 65dc04125dc2392d85a82b6916f8cb088ea77566
  React-jsitooling: 21af93cc98f760dd88d65b06b9317e0d4849fbbc
  React-jsitracing: 4cc1b7de8087ae41c61a0eeee2593bc3362908b6
  React-logger: 2f0d40bc8e648fbb1ff3b6580ad54189a8753290
  React-Mapbuffer: 9a7c65078c6851397c1999068989e4fc239d0c80
  React-microtasksnativemodule: 4f1ef719ba6c7ebbd2d75346ffa2916f9b4771c9
  react-native-safe-area-context: 339885703b6dd1be2bce42d9c0b0350c21180032
  React-NativeModulesApple: f6f696e510b9d89c3c06b7764f56947dc13ae922
  React-oscompat: 114036cd8f064558c9c1a0c04fc9ae5e1453706a
  React-perflogger: 4b2f88ae059b600daf268528a4a83366338eef05
  React-performancetimeline: e15fd9798123436f99e46898422fe921fecf506b
  React-RCTActionSheet: 68c68b0a7a5d2b0cfc255c64889b6e485974e988
  React-RCTAnimation: 6bf502c89c53076f92cd1a254f5ec8d63ee263de
  React-RCTAppDelegate: c90f5732784684c3dd226d812eccb578cd954ad7
  React-RCTBlob: d2905f01749b80efd6d3b86fb15e30ed26d5450b
  React-RCTFabric: 435b3ffaad113fb1f274c2f2a677c9fcc9b5cf55
  React-RCTFBReactNativeSpec: a3178b419f42af196e90ca4bf07710dce5d68301
  React-RCTImage: 8f5ffa03461339180a68820ea452af6e20ace2c7
  React-RCTLinking: 1151646834d31f97580d8a75d768a84b2533b7f9
  React-RCTNetwork: 52008724d0db90a540f4058ed0de0e41c4b7943c
  React-RCTRuntime: 10ce9a7cb27ba307544d29a2a04e6202dc7b3e9a
  React-RCTSettings: f724cacbd892ee18f985e1aebdd97386e49c76f5
  React-RCTText: 6e1b95d9126d808410dfa96e09bc4441ec6f36f7
  React-RCTVibration: 862a4e5b36d49e6299c8cbfb86486fc31f86f6fa
  React-rendererconsistency: f7baab26c6d0cd5b2eb7afcecfd2d8b957017b18
  React-renderercss: 62acb8f010a062309e3bd0e203aa14636162e3b3
  React-rendererdebug: 3a89ac44f15c7160735264d585a29525655238d2
  React-rncore: f7438473c4c71ee1963fb06a8635bb96013c9e1c
  React-RuntimeApple: 81f0a9ba81ce7eb203529b0471dc69bf18f5f637
  React-RuntimeCore: 6356e89b2518ba66a989c39a2adb18122a5e3b7b
  React-runtimeexecutor: 17c70842d5e611130cb66f91e247bc4a609c3508
  React-RuntimeHermes: 0a1d7ce2fe08cf182235de1a9330b51aa6b935cd
  React-runtimescheduler: 10ae98e1417eff159be5df8fdc8fcdaac557aba6
  React-timing: c3c923df2b86194e1682e01167717481232f1dc7
  React-utils: 7791a96e194eec85cb41dc98a2045b5f07839598
  ReactAppDependencyProvider: ba631a31783569c13056dd57ff39e19764abdd6f
  ReactCodegen: b16d00d43b4e9dc44af53be171b17d93b4b20267
  ReactCommon: 96684b90b235d6ae340d126141edd4563b7a446a
  RNFastImage: 5c9c9fed9c076e521b3f509fe79e790418a544e8
  RNFlashList: 995a7cad345dcb6f203db0f50bcf4b83cc04f3c9
  RNGestureHandler: c202f13fa95347076d8aca4ccb61739b067396cb
  RNImageCropPicker: 36d402fc475d75e1208f8b81ba294c84ac7f09eb
  RNReanimated: 2010262133feb53009f16faaa404042eed7da018
  RNScreens: 56beb7c3cdb9f2b81e60add3bccb483d75fb5773
  RNSVG: c73af7848d94ca3e8136a5191d055e3c1d6fedab
  RNVectorIcons: 417c003b0ce7ac7748aa548720fd7127d1d74ded
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  Yoga: 703055a9f39562521cdb8657162dfd80f8c174c3

PODFILE CHECKSUM: f32398c4a420fa1f1dba9f903857aca0688b52ed

COCOAPODS: 1.15.2
