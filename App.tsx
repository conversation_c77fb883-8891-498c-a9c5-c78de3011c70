import { LogBox, StatusBar, View } from 'react-native';
import React from 'react';
import RootStackNavigator from './src/navigations/RootStackNavigator';
import { colors } from './src/theme/theme';

const App = () => {
  LogBox.ignoreLogs([
    'VirtualizedLists should never be nested inside plain ScrollViews',
  ]);
  return (
    <View style={{ flex: 1, backgroundColor: colors.background_color }}>
      <StatusBar
        backgroundColor={colors.background_color}
        barStyle={'dark-content'}
      />
      <RootStackNavigator />
    </View>
  );
};

export default App;
