import React, { useState } from 'react';
import {
  View,
  Dimensions,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { LineChart } from 'react-native-gifted-charts';
import { appStyles, colors, fonts } from '../../theme/theme';
import { ChevronDown, HandFill } from '../../assets/svgs';

const screenWidth = Dimensions.get('window').width;
const customDataPoint = () => {
  return (
    <View
      style={{
        width: 20,
        height: 20,
        backgroundColor: colors.primary,
        borderWidth: 4,
        borderRadius: 10,
        borderColor: colors.white,
      }}
    />
  );
};
const data = [
  { value: 180, label: 'Mon', hideDataPoint: true },
  { value: 140, label: 'Tue', hideDataPoint: true },
  { value: 120, label: 'Wed', hideDataPoint: true },
  { value: 240, label: 'Thu', hideDataPoint: true },
  {
    value: 130,
    label: 'Fri',
    customDataPoint: customDataPoint,
  },
  { value: 200, label: 'Sat', hideDataPoint: true },
  { value: 150, label: 'Sun', hideDataPoint: true },
];

const GlucoseLineChart = () => {
  const [selectedRange, setSelectedRange] = useState('1Week');

  const timeRanges = ['1Day', '1Week', '1Mon', '1Year'];

  return (
    <View style={{ marginTop: 34 }}>
      <View style={styles.container}>
        {/* Glucose Value Display */}
        <View style={appStyles.flexRow}>
          <HandFill />
          <Text style={styles.readingText}>138</Text>
          <View style={appStyles.flexRow}>
            <Text style={styles.unitText}>mg/dl</Text>
            <ChevronDown stroke={colors.primary} />
          </View>
        </View>

        {/* Time Range Selector */}
        <View style={styles.rangeSelector}>
          {timeRanges.map(range => (
            <TouchableOpacity
              key={range}
              onPress={() => setSelectedRange(range)}
              style={[
                styles.rangeButton,
                selectedRange === range && styles.activeButton,
              ]}
            >
              <Text
                style={[
                  styles.rangeText,
                  selectedRange === range && styles.activeText,
                ]}
              >
                {range}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      <View style={{ height: 350 }}>
        <LineChart
          data={data}
          curved
          areaChart
          thickness={6}
          height={300}
          color={colors.primary}
          maxValue={250}
          noOfSections={3}
          yAxisTextStyle={{ color: 'lightgray' }}
          startFillColor={'#21A3F24D'}
          endFillColor={'#ebf2f7'}
          startOpacity={0.4}
          endOpacity={0.4}
          spacing={60} // 🔼 Increased gap between points (30 -> 60)
          backgroundColor={colors.background_color}
          rulesColor="gray"
          rulesType="solid"
          initialSpacing={30} // 🔼 Better padding at start
          yAxisColor={colors.background_color}
          xAxisColor={colors.background_color}
          dataPointsHeight={20}
          dataPointsWidth={20}
          rulesLength={screenWidth * 2} // 🔼 Extends scrollable width
          scrollAnimation
        />
      </View>
    </View>
  );
};

export default GlucoseLineChart;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  readingText: {
    fontSize: 54,
    lineHeight: 66,
    fontFamily: fonts.Catamaran_Bold,
    paddingHorizontal: 10,
  },
  unitText: {
    fontSize: 20,
    color: colors.primary,
    fontFamily: fonts.Catamaran_Medium,
    lineHeight: 20,
  },
  rangeSelector: {
    flexDirection: 'row',
    marginTop: 34,
    backgroundColor: colors.white,
    borderRadius: 12,
    // padding: 5,
  },
  rangeButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 10,
  },
  activeButton: {
    backgroundColor: '#007aff',
  },
  rangeText: {
    fontSize: 14,
    color: '#888',
  },
  activeText: {
    color: '#fff',
    fontWeight: '600',
  },
});
