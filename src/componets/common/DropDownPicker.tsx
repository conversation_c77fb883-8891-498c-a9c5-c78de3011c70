import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  FlatList,
  ViewStyle,
  TextStyle,
  LayoutAnimation,
  Platform,
  UIManager,
} from 'react-native';
import { colors, fonts } from '../../theme/theme';
import AppText from './AppText';
import { ArrowDown } from '../../assets/svgs';

// Enable LayoutAnimation for Android
if (
  Platform.OS === 'android' &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface DropdownOption {
  label: string;
  value: string;
}

interface Props {
  label: string;
  options: DropdownOption[];
  selectedValue?: string;
  onSelect: (value: string) => void;
  placeholder?: string;
  containerStyle?: ViewStyle;
  dropdownStyle?: ViewStyle;
  labelStyle?: TextStyle;
  valueStyle?: TextStyle;
  disabled?: boolean;
  maxHeight?: number; // Maximum height for dropdown options
}

const DropDownPicker: React.FC<Props> = ({
  label,
  options,
  selectedValue,
  onSelect,
  placeholder,
  containerStyle,
  dropdownStyle,
  labelStyle,
  valueStyle,
  disabled = false,
  maxHeight = 200,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [optionsHeight, setOptionsHeight] = useState(0);

  // Animations
  const labelAnimation = useRef(
    new Animated.Value(selectedValue ? 1 : 0),
  ).current;
  const rotationAnimation = useRef(new Animated.Value(0)).current;
  const opacityAnimation = useRef(new Animated.Value(0)).current;
  const heightAnimation = useRef(new Animated.Value(0)).current;

  const selectedOption = options.find(option => option.value === selectedValue);
  const hasValue = !!selectedValue;

  // Calculate options height based on number of items
  const calculateOptionsHeight = () => {
    const itemHeight = 48; // Height per option item
    const padding = 8; // Top padding
    const maxItems = Math.floor((maxHeight - padding) / itemHeight);
    const visibleItems = Math.min(options.length, maxItems);
    return visibleItems * itemHeight + padding;
  };

  useEffect(() => {
    const calculatedHeight = calculateOptionsHeight();
    setOptionsHeight(calculatedHeight);
  }, [options.length, maxHeight]);

  // Label animation effect - only animate when there's a selected value
  useEffect(() => {
    Animated.timing(labelAnimation, {
      toValue: hasValue ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [hasValue, labelAnimation]);

  // Handle dropdown toggle
  const handlePress = () => {
    if (disabled) return;

    const toValue = isOpen ? 0 : 1;

    // Animate arrow rotation with spring for smoother feel
    Animated.spring(rotationAnimation, {
      toValue,
      tension: 300,
      friction: 20,
      useNativeDriver: true,
    }).start();

    // Animate dropdown height and opacity with easing
    Animated.parallel([
      Animated.timing(heightAnimation, {
        toValue: toValue * optionsHeight,
        duration: 350,
        useNativeDriver: false,
      }),
      Animated.timing(opacityAnimation, {
        toValue,
        duration: 250,
        useNativeDriver: false,
      }),
    ]).start();

    setIsOpen(!isOpen);
    setIsFocused(!isOpen);
  };

  // Handle option selection
  const handleSelect = (value: string) => {
    // First close the dropdown with same animation as opening (but reversed)
    Animated.spring(rotationAnimation, {
      toValue: 0,
      tension: 300,
      friction: 20,
      useNativeDriver: true,
    }).start();

    Animated.parallel([
      Animated.timing(heightAnimation, {
        toValue: 0,
        duration: 350,
        useNativeDriver: false,
      }),
      Animated.timing(opacityAnimation, {
        toValue: 0,
        duration: 250,
        useNativeDriver: false,
      }),
    ]).start(() => {
      // After animation completes, update the selection
      onSelect(value);
      setIsOpen(false);
      setIsFocused(false);
    });
  };

  // Animated values for label
  const labelTop = labelAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [16, 4], // Keep label inside container
  });

  const labelFontSize = labelAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [16, 12],
  });

  const labelColor = labelAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [colors.grey_60, colors.grey_60],
  });

  const arrowRotation = rotationAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  // Smooth easing for better animation feel
  const animationConfig = {
    tension: 300,
    friction: 30,
    useNativeDriver: false,
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Main dropdown button */}
      <TouchableOpacity
        style={[
          styles.dropdown,
          dropdownStyle,
          (isFocused || isOpen) && styles.dropdownFocused,
          disabled && styles.dropdownDisabled,
          isOpen && styles.dropdownOpen,
        ]}
        onPress={handlePress}
        disabled={disabled}
        activeOpacity={0.7}
      >
        {/* Label - shows as default text when no selection, moves up when selected */}
        <Animated.Text
          style={[
            styles.label,
            labelStyle,
            {
              top: labelTop,
              fontSize: labelFontSize,
              color: labelColor,
            },
          ]}
        >
          {label}
        </Animated.Text>

        {/* Selected value - shows below the label */}
        {hasValue && (
          <AppText style={[styles.value, valueStyle]}>
            {selectedOption?.label || selectedValue}
          </AppText>
        )}

        {/* Arrow icon */}
        <Animated.View
          style={[
            styles.iconContainer,
            {
              transform: [{ rotate: arrowRotation }],
            },
          ]}
        >
          <ArrowDown />
        </Animated.View>
      </TouchableOpacity>

      {/* Options dropdown */}
      <Animated.View
        style={[
          styles.optionsWrapper,
          {
            height: heightAnimation,
            opacity: opacityAnimation,
          },
        ]}
        pointerEvents={isOpen ? 'auto' : 'none'}
      >
        <FlatList
          data={options}
          keyExtractor={item => item.value}
          renderItem={({ item, index }) => (
            <TouchableOpacity
              style={[
                styles.option,
                item.value === selectedValue && styles.selectedOption,
                index === options.length - 1 && styles.lastOption,
              ]}
              onPress={() => handleSelect(item.value)}
              activeOpacity={0.7}
            >
              <AppText
                style={[
                  styles.optionText,
                  item.value === selectedValue && styles.selectedOptionText,
                ]}
              >
                {item.label}
              </AppText>
            </TouchableOpacity>
          )}
          showsVerticalScrollIndicator={true}
          style={styles.optionsList}
          scrollEnabled={options.length * 48 > maxHeight - 8}
          nestedScrollEnabled={true}
        />
      </Animated.View>
    </View>
  );
};

export default DropDownPicker;

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    zIndex: 1000,
  },
  dropdown: {
    backgroundColor: colors.grey_10,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.grey_20,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    minHeight: 56,
    position: 'relative',
    justifyContent: 'center',
  },
  dropdownFocused: {
    borderColor: colors.primary,
  },
  dropdownOpen: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  dropdownDisabled: {
    backgroundColor: colors.grey_05,
    opacity: 0.6,
  },
  label: {
    position: 'absolute',
    left: 16,
    fontFamily: fonts.NotoSans_Regular,
    backgroundColor: 'transparent',
    paddingHorizontal: 0,
    zIndex: 2,
  },
  value: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.grey_90,
    lineHeight: 20,
    marginTop: 18, // Space below the label
  },
  placeholder: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_50,
    lineHeight: 20,
  },
  iconContainer: {
    position: 'absolute',
    right: 16,
    top: '70%',
    transform: [{ translateY: -10 }],
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionsWrapper: {
    backgroundColor: colors.grey_10,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderColor: colors.primary,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  optionsList: {
    paddingTop: 8,
  },
  option: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    minHeight: 48,
    justifyContent: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.grey_20,
  },
  lastOption: {
    borderBottomWidth: 0,
  },
  selectedOption: {
    backgroundColor: colors.cyan_blue,
  },
  optionText: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_90,
    lineHeight: 20,
  },
  selectedOptionText: {
    color: colors.primary,
    fontFamily: fonts.NotoSans_Medium,
  },
});
